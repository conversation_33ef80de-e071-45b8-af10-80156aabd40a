#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// List of known npm packages that should not have .js extensions
const npmPackages = [
  'express', 'cors', 'helmet', 'dotenv', 'bcryptjs', 'crypto', 'winston', 'path',
  'sequelize', 'joi', 'jsonwebtoken', 'multer', 'sharp', 'uuid', 'mammoth',
  'pdf-lib', 'xlsx', 'mysql2', '@langchain/openai', '@langchain/anthropic',
  '@langchain/core', '@pinecone-database/pinecone', 'langchain'
];

/**
 * Fix npm package imports that were incorrectly converted to relative imports
 */
function fixNpmImports(content) {
  npmPackages.forEach(packageName => {
    // Fix imports that were incorrectly converted to relative paths
    const incorrectPattern = new RegExp(`from\\s+['"\`]\\./${packageName}\\.js['"\`]`, 'g');
    content = content.replace(incorrectPattern, `from '${packageName}'`);
    
    // Also fix any that might have been converted without the ./
    const incorrectPattern2 = new RegExp(`from\\s+['"\`]${packageName}\\.js['"\`]`, 'g');
    content = content.replace(incorrectPattern2, `from '${packageName}'`);
  });
  
  // Fix specific @langchain imports
  content = content.replace(/from\s+['"`]\.?\/?@langchain\/([^'"`]+)\.js['"`]/g, "from '@langchain/$1'");
  content = content.replace(/from\s+['"`]\.?\/?@pinecone-database\/([^'"`]+)\.js['"`]/g, "from '@pinecone-database/$1'");
  
  return content;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Skip if file doesn't contain import statements
    if (!content.includes('from ')) {
      return;
    }
    
    console.log(`Fixing npm imports in: ${filePath}`);
    
    const originalContent = content;
    content = fixNpmImports(content);
    
    // Only write if content changed
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✓ Fixed: ${filePath}`);
    } else {
      console.log(`- No changes needed: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

/**
 * Recursively find and process JavaScript files
 */
function processDirectory(dirPath) {
  const entries = fs.readdirSync(dirPath, { withFileTypes: true });
  
  for (const entry of entries) {
    const fullPath = path.join(dirPath, entry.name);
    
    if (entry.isDirectory()) {
      processDirectory(fullPath);
    } else if (entry.isFile() && entry.name.endsWith('.js')) {
      processFile(fullPath);
    }
  }
}

// Main execution
const srcPath = path.join(__dirname, 'src');
console.log('Starting npm import fix...');
processDirectory(srcPath);
console.log('NPM import fix completed!');
