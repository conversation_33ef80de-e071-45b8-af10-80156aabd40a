#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Fix inline import statements by moving them to the top of the file
 */
function fixInlineImports(content) {
  const lines = content.split('\n');
  const topImports = [];
  const fixedLines = [];
  const seenImports = new Set();
  
  // First pass: collect existing top-level imports
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    if (line.trim().startsWith('import ') && !line.includes('    import ')) {
      seenImports.add(line.trim());
    }
  }
  
  // Second pass: find inline imports and collect them
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // Check for inline imports (indented with spaces)
    if (line.includes('    import ') && line.trim().startsWith('import ')) {
      const importStatement = line.trim();
      
      // Only add if we haven't seen this import before
      if (!seenImports.has(importStatement)) {
        topImports.push(importStatement);
        seenImports.add(importStatement);
      }
      
      // Skip this line (remove the inline import)
      continue;
    }
    
    fixedLines.push(line);
  }
  
  // If we found inline imports, add them to the top
  if (topImports.length > 0) {
    const newLines = [];
    let insertIndex = 0;
    
    // Find where to insert the new imports (after existing imports)
    for (let i = 0; i < fixedLines.length; i++) {
      const line = fixedLines[i];
      if (line.trim().startsWith('import ')) {
        insertIndex = i + 1;
      } else if (line.trim() === '' && insertIndex > 0) {
        // Keep going through empty lines after imports
        insertIndex = i + 1;
      } else if (insertIndex > 0 && line.trim() !== '') {
        // Found first non-import, non-empty line
        break;
      }
    }
    
    // Insert existing lines up to insertion point
    for (let i = 0; i < insertIndex; i++) {
      newLines.push(fixedLines[i]);
    }
    
    // Insert new imports
    topImports.forEach(importStatement => {
      newLines.push(importStatement);
    });
    
    // Add empty line if needed
    if (insertIndex < fixedLines.length && fixedLines[insertIndex].trim() !== '') {
      newLines.push('');
    }
    
    // Insert remaining lines
    for (let i = insertIndex; i < fixedLines.length; i++) {
      newLines.push(fixedLines[i]);
    }
    
    return newLines.join('\n');
  }
  
  return content;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Skip if file doesn't contain inline imports
    if (!content.includes('    import ')) {
      return;
    }
    
    console.log(`Fixing inline imports in: ${filePath}`);
    
    const originalContent = content;
    content = fixInlineImports(content);
    
    // Only write if content changed
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✓ Fixed: ${filePath}`);
    } else {
      console.log(`- No changes needed: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

/**
 * Recursively find and process JavaScript files
 */
function processDirectory(dirPath) {
  const entries = fs.readdirSync(dirPath, { withFileTypes: true });
  
  for (const entry of entries) {
    const fullPath = path.join(dirPath, entry.name);
    
    if (entry.isDirectory()) {
      processDirectory(fullPath);
    } else if (entry.isFile() && entry.name.endsWith('.js')) {
      processFile(fullPath);
    }
  }
}

// Main execution
const srcPath = path.join(__dirname, 'src');
console.log('Starting inline import fix...');
processDirectory(srcPath);
console.log('Inline import fix completed!');
