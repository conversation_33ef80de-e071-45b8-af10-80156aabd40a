import { DataTypes, Model  } from 'sequelize';
import { userDatabase  } from '../../config/database.js';
import { EncryptionUtil  } from '../../utils/encryption.js';

/**
 * UserCredit Model
 * Manages user credit balance for chat operations
 */
class UserCredit extends Model {
  /**
   * Add credits to user balance
   * @param {number} amount - Amount to add
   * @returns {Promise<void>}
   */
  async addCredits(amount) {
    this.credits += amount;
    await this.save();
  }

  /**
   * Deduct credits from user balance
   * @param {number} amount - Amount to deduct
   * @returns {Promise<boolean>} True if successful, false if insufficient credits
   */
  async deductCredits(amount) {
    if (this.credits < amount) {
      return false; // Insufficient credits
    }
    this.credits -= amount;
    await this.save();
    return true;
  }

  /**
   * Check if user has enough credits
   * @param {number} amount - Amount to check
   * @returns {boolean} True if user has enough credits
   */
  hasEnoughCredits(amount) {
    return this.credits >= amount;
  }

  /**
   * Create user credit record
   * @param {string} userId - User ID
   * @param {number} [initialCredits=50] - Initial credit amount
   * @returns {Promise<UserCredit>} Created credit record
   */
  static async createUserCredit(userId, initialCredits = 50) {
    return UserCredit.create({
      id: EncryptionUtil.generateUUID(),
      userId,
      credits: initialCredits,
    });
  }

  /**
   * Find credit record by user ID
   * @param {string} userId - User ID
   * @returns {Promise<UserCredit|null>} Credit record or null
   */
  static async findByUserId(userId) {
    return UserCredit.findOne({
      where: { userId },
    });
  }

  /**
   * Find or create credit record for user
   * @param {string} userId - User ID
   * @param {number} [initialCredits=50] - Initial credit amount if creating
   * @returns {Promise<[UserCredit, boolean]>} Tuple of [creditRecord, wasCreated]
   */
  static async findOrCreateByUserId(userId, initialCredits = 50) {
    const [userCredit, created] = await UserCredit.findOrCreate({
      where: { userId },
      defaults: {
        id: EncryptionUtil.generateUUID(),
        userId,
        credits: initialCredits,
      },
    });
    return [userCredit, created];
  }
}

UserCredit.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      // Note: Foreign key reference will be set up in associations
      unique: true, // One credit record per user
    },
    credits: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 50,
      validate: {
        min: 0,
      },
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: userDatabase,
    modelName: 'UserCredit',
    tableName: 'user_credits',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['user_id'],
      },
      {
        fields: ['credits'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['updated_at'],
      },
    ],
  }
);

export { UserCredit  };
