import { DataTypes, Model  } from 'sequelize';
import { chatDatabase  } from '../../config/database.js';

import { EncryptionUtil  } from '../../utils/encryption.js';

/**
 * ChatThread Model
 * Represents a chat thread for regular or project-based conversations
 */
class ChatThread extends Model {
  /**
   * Create a new chat thread
   * @param {Object} data - Thread data
   * @param {string} [data.userId] - User ID (optional for guest threads)
   * @param {string} [data.projectId] - Project ID (optional)
   * @param {string} data.sessionId - Session ID
   * @param {string} [data.name] - Thread name (optional)
   * @param {boolean} data.isGuest - Whether thread is from guest
   * @returns {Promise<ChatThread>} Created thread instance
   */
  static async createThread(data) {

    return ChatThread.create({
      id: EncryptionUtil.generateUUID(),
      userId: data.userId,
      projectId: data.projectId,
      sessionId: data.sessionId,
      name: data.name,
      isGuest: data.isGuest,
    });
  }

  /**
   * Find thread by session ID
   * @param {string} sessionId - Session ID
   * @returns {Promise<ChatThread|null>} Thread instance or null
   */
  static async findBySessionId(sessionId) {
    return ChatThread.findOne({
      where: { sessionId },
    });
  }

  /**
   * Find user threads (excluding project threads)
   * @param {string} userId - User ID
   * @param {number} [limit=6] - Limit number of results
   * @param {number} [offset=0] - Offset for pagination
   * @returns {Promise<ChatThread[]>} Array of thread instances
   */
  static async findUserThreads(userId, limit = 6, offset = 0) {
    return ChatThread.findAll({
      where: {
        userId,
        isGuest: false,
        projectId: null, // Only regular threads, not project threads
      },
      order: [['updatedAt', 'DESC']],
      limit,
      offset,
    });
  }

  /**
   * Find project threads
   * @param {string} projectId - Project ID
   * @param {number} [limit=6] - Limit number of results
   * @param {number} [offset=0] - Offset for pagination
   * @returns {Promise<ChatThread[]>} Array of thread instances
   */
  static async findProjectThreads(projectId, limit = 6, offset = 0) {
    return ChatThread.findAll({
      where: {
        projectId,
        isGuest: false,
      },
      order: [['updatedAt', 'DESC']],
      limit,
      offset,
    });
  }

  /**
   * Find guest threads by session ID
   * @param {string} sessionId - Session ID
   * @returns {Promise<ChatThread[]>} Array of thread instances
   */
  static async findGuestThreads(sessionId) {
    return ChatThread.findAll({
      where: {
        sessionId,
        isGuest: true,
      },
      order: [['createdAt', 'ASC']],
    });
  }

  /**
   * Find thread by ID and user
   * @param {string} id - Thread ID
   * @param {string} userId - User ID
   * @returns {Promise<ChatThread|null>} Thread instance or null
   */
  static async findByIdAndUser(id, userId) {
    return ChatThread.findOne({
      where: { id, userId },
    });
  }

  /**
   * Update thread name
   * @param {string} name - New name
   * @returns {Promise<void>}
   */
  async updateName(name) {
    this.name = name;
    await this.save();
  }

  /**
   * Convert guest thread to user thread
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  async convertToUserThread(userId) {
    this.userId = userId;
    this.isGuest = false;
    await this.save();
  }

  /**
   * Generate thread name from first message
   * @param {string} firstMessage - First message content
   * @returns {string} Generated thread name
   */
  generateThreadName(firstMessage) {
    return firstMessage.substring(0, 10).trim() + (firstMessage.length > 10 ? '...' : '');
  }
}

ChatThread.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: true,
    },
    projectId: {
      type: DataTypes.UUID,
      allowNull: true,
    },
    sessionId: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    isGuest: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: chatDatabase,
    modelName: 'ChatThread',
    tableName: 'chat_threads',
    timestamps: true,
    indexes: [
      {
        fields: ['user_id'],
      },
      {
        fields: ['project_id'],
      },
      {
        fields: ['session_id'],
      },
      {
        fields: ['is_guest'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['updated_at'],
      },
    ],
  }
);

export { ChatThread  };
