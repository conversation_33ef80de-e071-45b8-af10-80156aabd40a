#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Remove duplicate import statements
 */
function removeDuplicateImports(content) {
  const lines = content.split('\n');
  const seenImports = new Set();
  const filteredLines = [];
  
  for (const line of lines) {
    const trimmedLine = line.trim();
    
    // Check if this is an import statement
    if (trimmedLine.startsWith('import ')) {
      // Normalize the import statement for comparison
      const normalizedImport = trimmedLine.replace(/\s+/g, ' ');
      
      if (seenImports.has(normalizedImport)) {
        // Skip duplicate import
        console.log(`  Removing duplicate: ${trimmedLine}`);
        continue;
      }
      
      seenImports.add(normalizedImport);
    }
    
    filteredLines.push(line);
  }
  
  return filteredLines.join('\n');
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Skip if file doesn't contain import statements
    if (!content.includes('import ')) {
      return;
    }
    
    console.log(`Checking for duplicate imports in: ${filePath}`);
    
    const originalContent = content;
    content = removeDuplicateImports(content);
    
    // Only write if content changed
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✓ Fixed duplicates in: ${filePath}`);
    } else {
      console.log(`- No duplicates found: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

/**
 * Recursively find and process JavaScript files
 */
function processDirectory(dirPath) {
  const entries = fs.readdirSync(dirPath, { withFileTypes: true });
  
  for (const entry of entries) {
    const fullPath = path.join(dirPath, entry.name);
    
    if (entry.isDirectory()) {
      processDirectory(fullPath);
    } else if (entry.isFile() && entry.name.endsWith('.js')) {
      processFile(fullPath);
    }
  }
}

// Main execution
const srcPath = path.join(__dirname, 'src');
console.log('Starting duplicate import removal...');
processDirectory(srcPath);
console.log('Duplicate import removal completed!');
